"use client";

import { useCallback, useEffect, useRef, useState } from "react";

// Feature flags and configuration
const ENABLE_REALTIME = process.env.NEXT_PUBLIC_ENABLE_REALTIME === "true";
const MIGRATION_PHASE = process.env.NEXT_PUBLIC_PARTYKIT_MIGRATION_PHASE || "development";
const FALLBACK_ENABLED = process.env.NEXT_PUBLIC_ENABLE_FALLBACK !== "false";

export interface FallbackConfig {
  maxConnectionAttempts: number;
  connectionTimeout: number;
  healthCheckInterval: number;
  fallbackDelay: number;
  pollingInterval: number;
  enableAutoRecovery: boolean;
}

export interface ConnectionHealth {
  isHealthy: boolean;
  latency: number | null;
  lastSuccessfulPing: number | null;
  consecutiveFailures: number;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'failed';
}

export interface FallbackState {
  mode: 'realtime' | 'polling' | 'hybrid';
  reason: string | null;
  canRetryRealtime: boolean;
  nextRetryAt: number | null;
  connectionHealth: ConnectionHealth;
}

const DEFAULT_CONFIG: FallbackConfig = {
  maxConnectionAttempts: 3,
  connectionTimeout: 10000, // 10 seconds
  healthCheckInterval: 30000, // 30 seconds
  fallbackDelay: 5000, // 5 seconds
  pollingInterval: 30000, // 30 seconds
  enableAutoRecovery: true,
};

export class FallbackManager {
  private config: FallbackConfig;
  private state: FallbackState;
  private connectionAttempts: number = 0;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private retryTimeout: NodeJS.Timeout | null = null;
  private listeners: Set<(state: FallbackState) => void> = new Set();
  private lastPingTime: number | null = null;
  private pingTimeouts: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: Partial<FallbackConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.state = {
      mode: ENABLE_REALTIME ? 'realtime' : 'polling',
      reason: null,
      canRetryRealtime: true,
      nextRetryAt: null,
      connectionHealth: {
        isHealthy: false,
        latency: null,
        lastSuccessfulPing: null,
        consecutiveFailures: 0,
        connectionQuality: 'failed'
      }
    };

    this.startHealthChecking();
  }

  public getState(): FallbackState {
    return { ...this.state };
  }

  public subscribe(listener: (state: FallbackState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  public onConnectionSuccess(): void {
    this.connectionAttempts = 0;
    this.updateConnectionHealth({
      isHealthy: true,
      consecutiveFailures: 0,
      connectionQuality: 'excellent'
    });

    if (this.state.mode !== 'realtime') {
      this.updateState({
        mode: 'realtime',
        reason: null,
        canRetryRealtime: true,
        nextRetryAt: null
      });
    }
  }

  public onConnectionFailure(error: string): void {
    this.connectionAttempts++;
    
    const consecutiveFailures = this.state.connectionHealth.consecutiveFailures + 1;
    this.updateConnectionHealth({
      isHealthy: false,
      consecutiveFailures,
      connectionQuality: this.getConnectionQuality(consecutiveFailures)
    });

    if (this.shouldFallbackToPolling()) {
      this.fallbackToPolling(error);
    } else if (this.config.enableAutoRecovery) {
      this.scheduleRetry();
    }
  }

  public onLatencyUpdate(latency: number): void {
    this.updateConnectionHealth({
      latency,
      lastSuccessfulPing: Date.now(),
      connectionQuality: this.getConnectionQualityFromLatency(latency)
    });
  }

  public onPingResponse(pingId: string): void {
    const timeout = this.pingTimeouts.get(pingId);
    if (timeout) {
      clearTimeout(timeout);
      this.pingTimeouts.delete(pingId);
    }

    if (this.lastPingTime) {
      const latency = Date.now() - this.lastPingTime;
      this.onLatencyUpdate(latency);
    }
  }

  public sendPing(sendPingFunction: (pingId: string) => boolean): void {
    const pingId = `ping_${Date.now()}_${Math.random()}`;
    this.lastPingTime = Date.now();

    const success = sendPingFunction(pingId);
    
    if (success) {
      // Set timeout for ping response
      const timeout = setTimeout(() => {
        this.onPingTimeout(pingId);
      }, this.config.connectionTimeout);
      
      this.pingTimeouts.set(pingId, timeout);
    } else {
      this.onConnectionFailure('Failed to send ping');
    }
  }

  public forcePollingMode(reason: string): void {
    this.fallbackToPolling(reason);
  }

  public attemptRealtimeRecovery(): void {
    if (this.state.canRetryRealtime) {
      this.updateState({
        mode: 'realtime',
        reason: null
      });
      this.connectionAttempts = 0;
    }
  }

  public destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }

    // Clear all ping timeouts
    for (const timeout of this.pingTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.pingTimeouts.clear();

    this.listeners.clear();
  }

  private shouldFallbackToPolling(): boolean {
    if (!FALLBACK_ENABLED) return false;
    
    return this.connectionAttempts >= this.config.maxConnectionAttempts ||
           this.state.connectionHealth.consecutiveFailures >= 5;
  }

  private fallbackToPolling(reason: string): void {
    const canRetryRealtime = this.config.enableAutoRecovery && 
                            this.connectionAttempts < this.config.maxConnectionAttempts * 2;

    this.updateState({
      mode: 'polling',
      reason,
      canRetryRealtime,
      nextRetryAt: canRetryRealtime ? Date.now() + (this.config.fallbackDelay * 2) : null
    });

    if (canRetryRealtime) {
      this.scheduleRetry();
    }
  }

  private scheduleRetry(): void {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }

    const delay = this.config.fallbackDelay * Math.pow(2, Math.min(this.connectionAttempts, 5));
    
    this.retryTimeout = setTimeout(() => {
      if (this.state.canRetryRealtime) {
        this.attemptRealtimeRecovery();
      }
    }, delay);

    this.updateState({
      nextRetryAt: Date.now() + delay
    });
  }

  private onPingTimeout(pingId: string): void {
    this.pingTimeouts.delete(pingId);
    this.onConnectionFailure('Ping timeout');
  }

  private getConnectionQuality(consecutiveFailures: number): ConnectionHealth['connectionQuality'] {
    if (consecutiveFailures === 0) return 'excellent';
    if (consecutiveFailures <= 2) return 'good';
    if (consecutiveFailures <= 5) return 'poor';
    return 'failed';
  }

  private getConnectionQualityFromLatency(latency: number): ConnectionHealth['connectionQuality'] {
    if (latency < 100) return 'excellent';
    if (latency < 300) return 'good';
    if (latency < 1000) return 'poor';
    return 'failed';
  }

  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(() => {
      // Health check logic can be implemented here
      // For now, we'll just emit the current state
      this.notifyListeners();
    }, this.config.healthCheckInterval);
  }

  private updateState(updates: Partial<FallbackState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  private updateConnectionHealth(updates: Partial<ConnectionHealth>): void {
    this.state.connectionHealth = { ...this.state.connectionHealth, ...updates };
    this.notifyListeners();
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getState());
      } catch (error) {
        console.error('Error in fallback state listener:', error);
      }
    });
  }
}

// React hook for fallback management
export function useFallbackManager(config?: Partial<FallbackConfig>) {
  const [fallbackState, setFallbackState] = useState<FallbackState | null>(null);
  const managerRef = useRef<FallbackManager | null>(null);

  useEffect(() => {
    managerRef.current = new FallbackManager(config);
    
    const unsubscribe = managerRef.current.subscribe(setFallbackState);

    return () => {
      unsubscribe();
      if (managerRef.current) {
        managerRef.current.destroy();
      }
    };
  }, []);

  const onConnectionSuccess = useCallback(() => {
    managerRef.current?.onConnectionSuccess();
  }, []);

  const onConnectionFailure = useCallback((error: string) => {
    managerRef.current?.onConnectionFailure(error);
  }, []);

  const onLatencyUpdate = useCallback((latency: number) => {
    managerRef.current?.onLatencyUpdate(latency);
  }, []);

  const sendPing = useCallback((sendPingFunction: (pingId: string) => boolean) => {
    managerRef.current?.sendPing(sendPingFunction);
  }, []);

  const forcePollingMode = useCallback((reason: string) => {
    managerRef.current?.forcePollingMode(reason);
  }, []);

  const attemptRealtimeRecovery = useCallback(() => {
    managerRef.current?.attemptRealtimeRecovery();
  }, []);

  return {
    fallbackState,
    onConnectionSuccess,
    onConnectionFailure,
    onLatencyUpdate,
    sendPing,
    forcePollingMode,
    attemptRealtimeRecovery,
    
    // Utility functions
    isUsingRealtime: fallbackState?.mode === 'realtime',
    isUsingPolling: fallbackState?.mode === 'polling',
    canRetryRealtime: fallbackState?.canRetryRealtime || false,
    connectionQuality: fallbackState?.connectionHealth.connectionQuality || 'failed',
    latency: fallbackState?.connectionHealth.latency,
  };
}

// Utility function to determine the best strategy based on environment
export function getOptimalStrategy(): 'realtime' | 'polling' | 'hybrid' {
  // In development, prefer real-time for testing
  if (MIGRATION_PHASE === 'development') {
    return ENABLE_REALTIME ? 'realtime' : 'polling';
  }
  
  // In staging, use hybrid approach
  if (MIGRATION_PHASE === 'staging') {
    return 'hybrid';
  }
  
  // In production, be more conservative
  if (MIGRATION_PHASE === 'production') {
    return ENABLE_REALTIME ? 'hybrid' : 'polling';
  }
  
  return 'polling';
}

// Feature flag utilities
export const FeatureFlags = {
  isRealtimeEnabled: () => ENABLE_REALTIME,
  isFallbackEnabled: () => FALLBACK_ENABLED,
  getMigrationPhase: () => MIGRATION_PHASE,
  shouldUseRealtime: (feature: string) => {
    const baseEnabled = ENABLE_REALTIME;
    
    // Feature-specific overrides
    switch (feature) {
      case 'messaging':
        return baseEnabled && process.env.NEXT_PUBLIC_ENABLE_REALTIME_MESSAGING === 'true';
      case 'notifications':
        return baseEnabled && process.env.NEXT_PUBLIC_ENABLE_REALTIME_NOTIFICATIONS === 'true';
      case 'presence':
        return baseEnabled && process.env.NEXT_PUBLIC_ENABLE_REALTIME_PRESENCE !== 'false';
      default:
        return baseEnabled;
    }
  }
};
