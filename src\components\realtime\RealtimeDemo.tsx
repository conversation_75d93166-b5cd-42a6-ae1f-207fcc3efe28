"use client";

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRealtimeMessaging } from '@/hooks/useRealtimeMessaging';
import { useRealtimeNotifications } from '@/hooks/useRealtimeNotifications';
import { usePresence } from '@/hooks/usePresence';
import { useFallbackManager } from '@/lib/partykit/fallback';
import { usePerformanceMonitor } from '@/lib/partykit/performance';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

export function RealtimeDemo() {
  const { data: session } = useSession();
  const [testMessage, setTestMessage] = useState('');
  const [selectedUser, setSelectedUser] = useState('');

  // Real-time hooks
  const messaging = useRealtimeMessaging();
  const notifications = useRealtimeNotifications({
    enableSound: true,
    enableDesktopNotifications: true
  });
  const presence = usePresence();
  const fallback = useFallbackManager();
  const performance = usePerformanceMonitor();

  if (!session?.user) {
    return (
      <div className="p-6 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Real-time Demo</h2>
        <p>Please sign in to test real-time functionality.</p>
      </div>
    );
  }

  const handleSendTestMessage = async () => {
    if (!testMessage.trim() || !selectedUser) return;
    
    const success = await messaging.sendMessage(selectedUser, testMessage);
    if (success) {
      setTestMessage('');
    }
  };

  const handleStartTyping = () => {
    if (selectedUser) {
      presence.startTyping(selectedUser);
    }
  };

  const handleStopTyping = () => {
    if (selectedUser) {
      presence.stopTyping(selectedUser);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">PartyKit Real-time Demo</h2>
      
      {/* Connection Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900">Messaging</h3>
          <div className="mt-2 space-y-1 text-sm">
            <div className={`flex items-center gap-2 ${messaging.connected ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-2 h-2 rounded-full ${messaging.connected ? 'bg-green-500' : 'bg-red-500'}`} />
              {messaging.connected ? 'Connected' : 'Disconnected'}
            </div>
            <div className="text-gray-600">
              Mode: {messaging.usingRealtime ? 'Real-time' : 'Polling'}
            </div>
            {messaging.latency && (
              <div className="text-gray-600">
                Latency: {messaging.latency}ms
              </div>
            )}
          </div>
        </div>

        <div className="p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-900">Notifications</h3>
          <div className="mt-2 space-y-1 text-sm">
            <div className={`flex items-center gap-2 ${notifications.connected ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-2 h-2 rounded-full ${notifications.connected ? 'bg-green-500' : 'bg-red-500'}`} />
              {notifications.connected ? 'Connected' : 'Disconnected'}
            </div>
            <div className="text-gray-600">
              Mode: {notifications.usingRealtime ? 'Real-time' : 'Polling'}
            </div>
            <div className="text-gray-600">
              Unread: {notifications.notificationCounts.unread}
            </div>
          </div>
        </div>

        <div className="p-4 bg-purple-50 rounded-lg">
          <h3 className="font-semibold text-purple-900">Presence</h3>
          <div className="mt-2 space-y-1 text-sm">
            <div className={`flex items-center gap-2 ${presence.connected ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-2 h-2 rounded-full ${presence.connected ? 'bg-green-500' : 'bg-red-500'}`} />
              {presence.connected ? 'Connected' : 'Disconnected'}
            </div>
            <div className="text-gray-600">
              Status: {presence.currentStatus}
            </div>
            <div className="text-gray-600">
              Online Users: {presence.onlineCount}
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      {performance.metrics && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">Performance Metrics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-gray-600">Connection Quality</div>
              <div className={`font-semibold ${
                performance.metrics.connectionQuality === 'excellent' ? 'text-green-600' :
                performance.metrics.connectionQuality === 'good' ? 'text-blue-600' :
                performance.metrics.connectionQuality === 'poor' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {performance.metrics.connectionQuality}
              </div>
            </div>
            <div>
              <div className="text-gray-600">Avg Latency</div>
              <div className="font-semibold">
                {performance.metrics.averageLatency?.toFixed(2) || 'N/A'}ms
              </div>
            </div>
            <div>
              <div className="text-gray-600">Messages</div>
              <div className="font-semibold">{performance.metrics.totalMessages}</div>
            </div>
            <div>
              <div className="text-gray-600">Success Rate</div>
              <div className="font-semibold">
                {((1 - (performance.metrics.failedMessages / Math.max(performance.metrics.totalMessages, 1))) * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Fallback Status */}
      {fallback.fallbackState && (
        <div className="p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-900 mb-3">Fallback Status</h3>
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-gray-600">Mode:</span> 
              <span className="ml-2 font-semibold">{fallback.fallbackState.mode}</span>
            </div>
            {fallback.fallbackState.reason && (
              <div>
                <span className="text-gray-600">Reason:</span> 
                <span className="ml-2 text-red-600">{fallback.fallbackState.reason}</span>
              </div>
            )}
            <div>
              <span className="text-gray-600">Connection Quality:</span> 
              <span className="ml-2 font-semibold">{fallback.connectionQuality}</span>
            </div>
            {fallback.canRetryRealtime && (
              <Button
                onClick={fallback.attemptRealtimeRecovery}
                className="mt-2 px-3 py-1 text-xs"
              >
                Retry Real-time
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Test Controls */}
      <div className="p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-3">Test Real-time Features</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Test User ID
            </label>
            <Input
              value={selectedUser}
              onChange={(e) => setSelectedUser(e.target.value)}
              placeholder="Enter user ID to test messaging"
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Test Message
            </label>
            <div className="flex gap-2">
              <Input
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="Type a test message"
                className="flex-1"
                onFocus={handleStartTyping}
                onBlur={handleStopTyping}
              />
              <Button
                onClick={handleSendTestMessage}
                disabled={!testMessage.trim() || !selectedUser || messaging.isSending}
              >
                {messaging.isSending ? 'Sending...' : 'Send'}
              </Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={() => presence.updatePresenceStatus('online')}
              variant={presence.currentStatus === 'online' ? 'default' : 'outline'}
              size="sm"
            >
              Online
            </Button>
            <Button
              onClick={() => presence.updatePresenceStatus('away')}
              variant={presence.currentStatus === 'away' ? 'default' : 'outline'}
              size="sm"
            >
              Away
            </Button>
            <Button
              onClick={() => presence.updatePresenceStatus('busy')}
              variant={presence.currentStatus === 'busy' ? 'default' : 'outline'}
              size="sm"
            >
              Busy
            </Button>
          </div>
        </div>
      </div>

      {/* Recent Messages */}
      {messaging.messages.length > 0 && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">Recent Messages</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {messaging.messages.slice(-5).map((message) => (
              <div key={message.id} className="p-2 bg-white rounded text-sm">
                <div className="flex justify-between items-start">
                  <div>
                    <span className="font-medium">
                      {message.senderId === session.user.id ? 'You' : message.senderId}
                    </span>
                    <span className="text-gray-600 ml-2">{message.content}</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {message.status && (
                      <span className={`inline-block w-2 h-2 rounded-full mr-1 ${
                        message.status === 'read' ? 'bg-blue-500' :
                        message.status === 'delivered' ? 'bg-green-500' :
                        message.status === 'sent' ? 'bg-yellow-500' :
                        message.status === 'failed' ? 'bg-red-500' : 'bg-gray-500'
                      }`} />
                    )}
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Notifications */}
      {notifications.notifications.length > 0 && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">Recent Notifications</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {notifications.notifications.slice(0, 5).map((notification) => (
              <div key={notification.id} className="p-2 bg-white rounded text-sm">
                <div className="flex justify-between items-start">
                  <div className={notification.read ? 'opacity-60' : ''}>
                    <div className="font-medium">{notification.title}</div>
                    <div className="text-gray-600">{notification.message}</div>
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(notification.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
