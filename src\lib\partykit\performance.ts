"use client";

// Performance monitoring and optimization for PartyKit connections

export interface PerformanceMetrics {
  connectionLatency: number | null;
  messageLatency: number | null;
  reconnectionCount: number;
  totalMessages: number;
  failedMessages: number;
  connectionUptime: number;
  lastConnectionTime: number | null;
  averageLatency: number | null;
  peakLatency: number | null;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'failed';
}

export interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  totalDataTransferred: number;
  messagesPerSecond: number;
  errorRate: number;
}

export interface OptimizationSettings {
  maxConnections: number;
  connectionPooling: boolean;
  messageBuffering: boolean;
  bufferSize: number;
  bufferTimeout: number;
  compressionEnabled: boolean;
  heartbeatInterval: number;
  idleTimeout: number;
}

const DEFAULT_OPTIMIZATION_SETTINGS: OptimizationSettings = {
  maxConnections: 5,
  connectionPooling: true,
  messageBuffering: true,
  bufferSize: 10,
  bufferTimeout: 100, // ms
  compressionEnabled: true,
  heartbeatInterval: 30000, // 30 seconds
  idleTimeout: 300000, // 5 minutes
};

export class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private connectionStats: ConnectionStats;
  private settings: OptimizationSettings;
  private latencyHistory: number[] = [];
  private messageBuffer: any[] = [];
  private bufferTimeout: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionStartTime: number | null = null;
  private lastActivityTime: number = Date.now();
  private listeners: Set<(metrics: PerformanceMetrics) => void> = new Set();

  constructor(settings: Partial<OptimizationSettings> = {}) {
    this.settings = { ...DEFAULT_OPTIMIZATION_SETTINGS, ...settings };
    this.metrics = {
      connectionLatency: null,
      messageLatency: null,
      reconnectionCount: 0,
      totalMessages: 0,
      failedMessages: 0,
      connectionUptime: 0,
      lastConnectionTime: null,
      averageLatency: null,
      peakLatency: null,
      connectionQuality: 'failed'
    };
    this.connectionStats = {
      totalConnections: 0,
      activeConnections: 0,
      totalDataTransferred: 0,
      messagesPerSecond: 0,
      errorRate: 0
    };

    this.startHeartbeat();
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public getConnectionStats(): ConnectionStats {
    return { ...this.connectionStats };
  }

  public getSettings(): OptimizationSettings {
    return { ...this.settings };
  }

  public updateSettings(newSettings: Partial<OptimizationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
  }

  public subscribe(listener: (metrics: PerformanceMetrics) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  public onConnectionStart(): void {
    this.connectionStartTime = Date.now();
    this.metrics.lastConnectionTime = this.connectionStartTime;
    this.connectionStats.totalConnections++;
    this.connectionStats.activeConnections++;
    this.lastActivityTime = Date.now();
    this.notifyListeners();
  }

  public onConnectionEnd(): void {
    if (this.connectionStartTime) {
      const uptime = Date.now() - this.connectionStartTime;
      this.metrics.connectionUptime += uptime;
      this.connectionStartTime = null;
    }
    this.connectionStats.activeConnections = Math.max(0, this.connectionStats.activeConnections - 1);
    this.notifyListeners();
  }

  public onReconnection(): void {
    this.metrics.reconnectionCount++;
    this.connectionStats.totalConnections++;
    this.notifyListeners();
  }

  public recordLatency(latency: number, type: 'connection' | 'message' = 'message'): void {
    this.latencyHistory.push(latency);
    
    // Keep only last 100 measurements
    if (this.latencyHistory.length > 100) {
      this.latencyHistory.shift();
    }

    if (type === 'connection') {
      this.metrics.connectionLatency = latency;
    } else {
      this.metrics.messageLatency = latency;
    }

    // Update statistics
    this.metrics.averageLatency = this.latencyHistory.reduce((sum, l) => sum + l, 0) / this.latencyHistory.length;
    this.metrics.peakLatency = Math.max(...this.latencyHistory);
    this.metrics.connectionQuality = this.calculateConnectionQuality(latency);
    
    this.lastActivityTime = Date.now();
    this.notifyListeners();
  }

  public recordMessage(success: boolean, dataSize: number = 0): void {
    this.metrics.totalMessages++;
    this.connectionStats.totalDataTransferred += dataSize;
    
    if (!success) {
      this.metrics.failedMessages++;
    }

    // Calculate error rate
    this.connectionStats.errorRate = this.metrics.failedMessages / this.metrics.totalMessages;
    
    this.lastActivityTime = Date.now();
    this.notifyListeners();
  }

  public shouldBufferMessage(message: any): boolean {
    if (!this.settings.messageBuffering) return false;
    
    this.messageBuffer.push({
      message,
      timestamp: Date.now()
    });

    if (this.messageBuffer.length >= this.settings.bufferSize) {
      this.flushBuffer();
      return true;
    }

    if (!this.bufferTimeout) {
      this.bufferTimeout = setTimeout(() => {
        this.flushBuffer();
      }, this.settings.bufferTimeout);
    }

    return true;
  }

  public flushBuffer(): any[] {
    if (this.bufferTimeout) {
      clearTimeout(this.bufferTimeout);
      this.bufferTimeout = null;
    }

    const messages = this.messageBuffer.splice(0);
    return messages.map(item => item.message);
  }

  public shouldCreateNewConnection(): boolean {
    return this.connectionStats.activeConnections < this.settings.maxConnections;
  }

  public isConnectionIdle(): boolean {
    const idleTime = Date.now() - this.lastActivityTime;
    return idleTime > this.settings.idleTimeout;
  }

  public getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.averageLatency && this.metrics.averageLatency > 500) {
      recommendations.push("High latency detected. Consider enabling message buffering or compression.");
    }
    
    if (this.connectionStats.errorRate > 0.1) {
      recommendations.push("High error rate detected. Check network stability or reduce message frequency.");
    }
    
    if (this.metrics.reconnectionCount > 5) {
      recommendations.push("Frequent reconnections detected. Consider implementing exponential backoff.");
    }
    
    if (this.connectionStats.activeConnections > this.settings.maxConnections * 0.8) {
      recommendations.push("High connection usage. Consider connection pooling or increasing limits.");
    }

    if (this.isConnectionIdle()) {
      recommendations.push("Connection is idle. Consider closing to save resources.");
    }

    return recommendations;
  }

  public generateReport(): string {
    const metrics = this.getMetrics();
    const stats = this.getConnectionStats();
    const recommendations = this.getOptimizationRecommendations();

    return `
PartyKit Performance Report
==========================

Connection Metrics:
- Quality: ${metrics.connectionQuality}
- Average Latency: ${metrics.averageLatency?.toFixed(2) || 'N/A'}ms
- Peak Latency: ${metrics.peakLatency?.toFixed(2) || 'N/A'}ms
- Connection Uptime: ${(metrics.connectionUptime / 1000).toFixed(2)}s
- Reconnections: ${metrics.reconnectionCount}

Message Statistics:
- Total Messages: ${metrics.totalMessages}
- Failed Messages: ${metrics.failedMessages}
- Success Rate: ${((1 - stats.errorRate) * 100).toFixed(2)}%
- Data Transferred: ${(stats.totalDataTransferred / 1024).toFixed(2)}KB

Connection Statistics:
- Total Connections: ${stats.totalConnections}
- Active Connections: ${stats.activeConnections}
- Messages/Second: ${stats.messagesPerSecond.toFixed(2)}

Recommendations:
${recommendations.length > 0 ? recommendations.map(r => `- ${r}`).join('\n') : '- No recommendations at this time'}
    `.trim();
  }

  public destroy(): void {
    if (this.bufferTimeout) {
      clearTimeout(this.bufferTimeout);
    }
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.listeners.clear();
    this.messageBuffer = [];
    this.latencyHistory = [];
  }

  private calculateConnectionQuality(latency: number): PerformanceMetrics['connectionQuality'] {
    if (latency < 100) return 'excellent';
    if (latency < 300) return 'good';
    if (latency < 1000) return 'poor';
    return 'failed';
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.updateConnectionUptime();
      this.calculateMessagesPerSecond();
      this.notifyListeners();
    }, this.settings.heartbeatInterval);
  }

  private updateConnectionUptime(): void {
    if (this.connectionStartTime) {
      const currentUptime = Date.now() - this.connectionStartTime;
      this.metrics.connectionUptime = currentUptime;
    }
  }

  private calculateMessagesPerSecond(): void {
    // Simple calculation based on total messages and uptime
    const uptimeSeconds = this.metrics.connectionUptime / 1000;
    if (uptimeSeconds > 0) {
      this.connectionStats.messagesPerSecond = this.metrics.totalMessages / uptimeSeconds;
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getMetrics());
      } catch (error) {
        console.error('Error in performance metrics listener:', error);
      }
    });
  }
}

// React hook for performance monitoring
export function usePerformanceMonitor(settings?: Partial<OptimizationSettings>) {
  const [monitor] = useState(() => new PerformanceMonitor(settings));
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);

  useEffect(() => {
    const unsubscribe = monitor.subscribe(setMetrics);
    
    // Initial metrics
    setMetrics(monitor.getMetrics());

    return () => {
      unsubscribe();
      monitor.destroy();
    };
  }, [monitor]);

  return {
    monitor,
    metrics,
    connectionStats: monitor.getConnectionStats(),
    recommendations: monitor.getOptimizationRecommendations(),
    report: monitor.generateReport(),
    
    // Actions
    recordLatency: monitor.recordLatency.bind(monitor),
    recordMessage: monitor.recordMessage.bind(monitor),
    onConnectionStart: monitor.onConnectionStart.bind(monitor),
    onConnectionEnd: monitor.onConnectionEnd.bind(monitor),
    onReconnection: monitor.onReconnection.bind(monitor),
    
    // Utilities
    shouldBufferMessage: monitor.shouldBufferMessage.bind(monitor),
    flushBuffer: monitor.flushBuffer.bind(monitor),
    shouldCreateNewConnection: monitor.shouldCreateNewConnection.bind(monitor),
    isConnectionIdle: monitor.isConnectionIdle.bind(monitor),
  };
}

// Cost optimization utilities
export const CostOptimization = {
  calculateMonthlyCost: (metrics: PerformanceMetrics, stats: ConnectionStats) => {
    // Rough estimation based on PartyKit pricing
    const connectionsPerMonth = stats.totalConnections * 30; // Assuming daily usage
    const messagesPerMonth = metrics.totalMessages * 30;
    const dataTransferGB = (stats.totalDataTransferred / (1024 * 1024 * 1024)) * 30;
    
    // Estimated costs (these would need to be updated based on actual PartyKit pricing)
    const connectionCost = connectionsPerMonth * 0.001; // $0.001 per connection
    const messageCost = messagesPerMonth * 0.0001; // $0.0001 per message
    const dataCost = dataTransferGB * 0.1; // $0.1 per GB
    
    return {
      connections: connectionCost,
      messages: messageCost,
      dataTransfer: dataCost,
      total: connectionCost + messageCost + dataCost
    };
  },

  getOptimizationTips: (metrics: PerformanceMetrics, stats: ConnectionStats) => {
    const tips: string[] = [];
    
    if (stats.activeConnections > 3) {
      tips.push("Consider connection pooling to reduce concurrent connections");
    }
    
    if (metrics.totalMessages > 1000) {
      tips.push("Enable message buffering to reduce API calls");
    }
    
    if (stats.totalDataTransferred > 1024 * 1024) { // 1MB
      tips.push("Enable compression to reduce data transfer costs");
    }
    
    return tips;
  }
};
