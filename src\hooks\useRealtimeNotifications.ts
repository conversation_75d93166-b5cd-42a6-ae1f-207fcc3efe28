"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { usePartyKit, partyKitManager, type NotificationMessage } from "@/lib/partykit/client";

// Feature flags
const ENABLE_REALTIME_NOTIFICATIONS = process.env.NEXT_PUBLIC_ENABLE_REALTIME_NOTIFICATIONS === "true";

export interface NotificationItem {
  id: string;
  type: 'like' | 'comment' | 'share' | 'follow' | 'mention' | 'message' | 'fan_page_message' | 'fan_page_reply' | 'system';
  title: string;
  message: string;
  avatar?: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: Record<string, any>;
  senderId?: string;
}

export interface NotificationCounts {
  total: number;
  unread: number;
  byType: Record<string, number>;
  priority: {
    high: number;
    medium: number;
    low: number;
  };
}

interface UseRealtimeNotificationsOptions {
  fallbackToPolling?: boolean;
  pollingInterval?: number;
  enableSound?: boolean;
  enableDesktopNotifications?: boolean;
}

export function useRealtimeNotifications(options: UseRealtimeNotificationsOptions = {}) {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [usePolling, setUsePolling] = useState(!ENABLE_REALTIME_NOTIFICATIONS);

  // PartyKit connection for notifications
  const notificationConnection = usePartyKit("notifications", "notifications");
  
  // Refs for cleanup and state management
  const pollingIntervalRef = useRef<NodeJS.Timeout>();
  const audioRef = useRef<HTMLAudioElement>();

  // Determine if we should use real-time or polling
  const shouldUseRealtime = ENABLE_REALTIME_NOTIFICATIONS && 
                           notificationConnection.connected && 
                           !usePolling &&
                           session?.user?.id;

  // Initialize audio for notifications
  useEffect(() => {
    if (options.enableSound && typeof window !== 'undefined') {
      audioRef.current = new Audio('/sounds/notification.mp3');
      audioRef.current.volume = 0.5;
    }
  }, [options.enableSound]);

  // Request desktop notification permission
  useEffect(() => {
    if (options.enableDesktopNotifications && typeof window !== 'undefined' && 'Notification' in window) {
      if (Notification.permission === 'default') {
        Notification.requestPermission();
      }
    }
  }, [options.enableDesktopNotifications]);

  // Fallback to polling if real-time fails
  useEffect(() => {
    if (ENABLE_REALTIME_NOTIFICATIONS && !notificationConnection.connected && !notificationConnection.reconnecting) {
      if (options.fallbackToPolling !== false) {
        console.log("Falling back to polling for notifications due to connection issues");
        setUsePolling(true);
      }
    } else if (notificationConnection.connected) {
      setUsePolling(false);
    }
  }, [notificationConnection.connected, notificationConnection.reconnecting, options.fallbackToPolling]);

  // Fetch notifications from API
  const fetchNotifications = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      setIsLoading(true);
      const response = await fetch('/api/notifications');
      
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }
      
      const data = await response.json();
      setNotifications(data.notifications || []);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load notifications';
      setError(errorMessage);
      console.error('Error fetching notifications:', err);
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      if (shouldUseRealtime) {
        // Send via PartyKit
        notificationConnection.sendMessage({
          type: 'notification_read',
          notificationId,
          userId: session?.user?.id,
          timestamp: Date.now()
        });
      }

      // Update API
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH'
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }

      // Update local state
      setNotifications(prev => prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      ));

    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
    }
  }, [shouldUseRealtime, notificationConnection, session?.user?.id]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH'
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      // Update local state
      setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));

      // If using real-time, broadcast to other connections
      if (shouldUseRealtime) {
        const unreadNotifications = notifications.filter(n => !n.read);
        unreadNotifications.forEach(notification => {
          notificationConnection.sendMessage({
            type: 'notification_read',
            notificationId: notification.id,
            userId: session?.user?.id,
            timestamp: Date.now()
          });
        });
      }

    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark all notifications as read');
    }
  }, [shouldUseRealtime, notificationConnection, session?.user?.id, notifications]);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      if (shouldUseRealtime) {
        // Send via PartyKit
        notificationConnection.sendMessage({
          type: 'notification_delete',
          notificationId,
          userId: session?.user?.id,
          timestamp: Date.now()
        });
      }

      // Update API
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }

      // Update local state
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId));

    } catch (err) {
      console.error('Error deleting notification:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete notification');
    }
  }, [shouldUseRealtime, notificationConnection, session?.user?.id]);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    if (options.enableSound && audioRef.current) {
      audioRef.current.play().catch(err => {
        console.log('Could not play notification sound:', err);
      });
    }
  }, [options.enableSound]);

  // Show desktop notification
  const showDesktopNotification = useCallback((notification: NotificationItem) => {
    if (options.enableDesktopNotifications && 
        typeof window !== 'undefined' && 
        'Notification' in window && 
        Notification.permission === 'granted') {
      
      new Notification(notification.title, {
        body: notification.message,
        icon: notification.avatar || '/logo.png',
        tag: notification.id,
        requireInteraction: notification.priority === 'high'
      });
    }
  }, [options.enableDesktopNotifications]);

  // Handle incoming real-time notifications
  const handleIncomingNotification = useCallback((notificationMessage: NotificationMessage) => {
    const newNotification: NotificationItem = {
      id: notificationMessage.notificationId,
      type: notificationMessage.notificationType as NotificationItem['type'],
      title: notificationMessage.title,
      message: notificationMessage.message,
      avatar: notificationMessage.avatar,
      timestamp: new Date(notificationMessage.timestamp).toISOString(),
      read: false,
      priority: notificationMessage.priority,
      actionUrl: notificationMessage.actionUrl,
      metadata: notificationMessage.metadata,
      senderId: notificationMessage.senderId
    };

    setNotifications(prev => {
      // Avoid duplicates
      if (prev.some(n => n.id === newNotification.id)) return prev;
      return [newNotification, ...prev];
    });

    // Play sound and show desktop notification
    playNotificationSound();
    showDesktopNotification(newNotification);

  }, [playNotificationSound, showDesktopNotification]);

  // Real-time notification handlers
  useEffect(() => {
    if (!shouldUseRealtime) return;

    const unsubscribeNotification = notificationConnection.onMessage('notification', handleIncomingNotification);

    const unsubscribeNotificationRead = notificationConnection.onMessage('notification_read', (message: any) => {
      setNotifications(prev => prev.map(notification => 
        notification.id === message.notificationId 
          ? { ...notification, read: true }
          : notification
      ));
    });

    const unsubscribeNotificationDelete = notificationConnection.onMessage('notification_delete', (message: any) => {
      setNotifications(prev => prev.filter(notification => notification.id !== message.notificationId));
    });

    return () => {
      unsubscribeNotification();
      unsubscribeNotificationRead();
      unsubscribeNotificationDelete();
    };
  }, [shouldUseRealtime, notificationConnection, handleIncomingNotification]);

  // Polling fallback
  useEffect(() => {
    if (!usePolling || !session?.user?.id) return;

    const interval = options.pollingInterval || 30000; // Default 30 seconds
    
    // Initial fetch
    fetchNotifications();
    
    // Set up polling
    pollingIntervalRef.current = setInterval(fetchNotifications, interval);

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [usePolling, session?.user?.id, fetchNotifications, options.pollingInterval]);

  // Calculate notification counts
  const notificationCounts: NotificationCounts = {
    total: notifications.length,
    unread: notifications.filter(n => !n.read).length,
    byType: notifications.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    priority: {
      high: notifications.filter(n => n.priority === 'high' && !n.read).length,
      medium: notifications.filter(n => n.priority === 'medium' && !n.read).length,
      low: notifications.filter(n => n.priority === 'low' && !n.read).length,
    }
  };

  // Refresh notifications
  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await fetchNotifications();
    } finally {
      setIsLoading(false);
    }
  }, [fetchNotifications]);

  return {
    // State
    notifications,
    notificationCounts,
    isLoading,
    error,
    
    // Connection info
    connected: shouldUseRealtime ? notificationConnection.connected : true,
    reconnecting: notificationConnection.reconnecting,
    latency: notificationConnection.latency,
    usingRealtime: shouldUseRealtime,
    
    // Actions
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
    
    // Utilities
    getUnreadCount: () => notificationCounts.unread,
    getNotificationsByType: (type: string) => notifications.filter(n => n.type === type),
    getHighPriorityNotifications: () => notifications.filter(n => n.priority === 'high' && !n.read),
  };
}
