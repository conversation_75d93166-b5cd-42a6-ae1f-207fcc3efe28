"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRealtimeNotifications } from './useRealtimeNotifications';

export interface NotificationItem {
  id: string;
  type: 'like' | 'comment' | 'share' | 'follow' | 'mention' | 'message' | 'system';
  title: string;
  message: string;
  avatar?: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export interface MessageThread {
  id: string;
  participantId: string;
  participantName: string;
  participantAvatar?: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  isOnline?: boolean;
}

interface NotificationCounts {
  total: number;
  unread: number;
  byType: Record<string, number>;
  priority: {
    high: number;
    medium: number;
    low: number;
  };
}

interface MessageCounts {
  total: number;
  unread: number;
  threads: number;
}

export function useNotificationCount() {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<MessageThread[]>([]);
  const pollingRef = useRef<NodeJS.Timeout>();

  // Use real-time notifications with fallback to polling
  const {
    notifications,
    notificationCounts,
    isLoading,
    error,
    connected: notificationsConnected,
    usingRealtime: notificationsUsingRealtime,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh: refreshNotifications
  } = useRealtimeNotifications({
    fallbackToPolling: true,
    pollingInterval: 30000,
    enableSound: true,
    enableDesktopNotifications: true
  });

  // Notification counts are now provided by the real-time hook

  // Calculate message counts
  const messageCounts: MessageCounts = {
    total: messages.reduce((sum, thread) => sum + thread.unreadCount, 0),
    unread: messages.filter(thread => thread.unreadCount > 0).length,
    threads: messages.length
  };

  // Legacy fetch notifications (now handled by real-time hook)
  const fetchNotifications = useCallback(async () => {
    // This is now handled by the real-time notifications hook
    await refreshNotifications();
  }, [refreshNotifications]);

  // Fetch messages
  const fetchMessages = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/messages/threads');
      if (!response.ok) throw new Error('Failed to fetch messages');
      
      const data = await response.json();
      setMessages(data.threads || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    }
  }, [session?.user?.id]);

  // Mark notification as read
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
        );
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllNotificationsAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'POST'
      });
      
      if (response.ok) {
        setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, []);

  // Mark message thread as read
  const markMessageThreadAsRead = useCallback(async (threadId: string) => {
    try {
      const response = await fetch(`/api/messages/threads/${threadId}/read`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setMessages(prev => 
          prev.map(m => m.id === threadId ? { ...m, unreadCount: 0 } : m)
        );
      }
    } catch (error) {
      console.error('Error marking message thread as read:', error);
    }
  }, []);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }, []);

  // Refresh data
  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await Promise.all([fetchNotifications(), fetchMessages()]);
    } finally {
      setIsLoading(false);
    }
  }, [fetchNotifications, fetchMessages]);

  // Setup polling for messages only (notifications are now real-time)
  useEffect(() => {
    if (!session?.user?.id) return;

    // Initial fetch for messages
    fetchMessages();

    // Setup polling every 30 seconds for messages only
    pollingRef.current = setInterval(() => {
      fetchMessages();
    }, 30000);

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, [session?.user?.id, fetchMessages]);

  // Listen for real-time updates (if WebSocket is available)
  useEffect(() => {
    const handleNotificationUpdate = (event: CustomEvent) => {
      const { type, data } = event.detail;
      
      if (type === 'new_notification') {
        setNotifications(prev => [data, ...prev]);
      } else if (type === 'new_message') {
        setMessages(prev => {
          const existingThread = prev.find(m => m.participantId === data.senderId);
          if (existingThread) {
            return prev.map(m => 
              m.participantId === data.senderId 
                ? { 
                    ...m, 
                    lastMessage: data.content,
                    timestamp: data.timestamp,
                    unreadCount: m.unreadCount + 1
                  }
                : m
            );
          } else {
            return [{
              id: data.threadId,
              participantId: data.senderId,
              participantName: data.senderName,
              participantAvatar: data.senderAvatar,
              lastMessage: data.content,
              timestamp: data.timestamp,
              unreadCount: 1
            }, ...prev];
          }
        });
      }
    };

    window.addEventListener('notification_update', handleNotificationUpdate as EventListener);
    
    return () => {
      window.removeEventListener('notification_update', handleNotificationUpdate as EventListener);
    };
  }, []);

  return {
    notifications,
    messages,
    notificationCounts,
    messageCounts,
    isLoading,
    error,

    // Real-time notification actions
    markAsRead,
    markAllAsRead,
    deleteNotification,

    // Legacy message actions
    markMessageThreadAsRead,

    // Connection status
    connected: notificationsConnected,
    usingRealtime: notificationsUsingRealtime,

    // Refresh functions
    refresh: refreshNotifications,
    refreshMessages: fetchMessages
  };
}
