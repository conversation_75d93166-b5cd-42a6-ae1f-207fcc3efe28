"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { usePartyKit } from "@/lib/partykit/client";

// Feature flags
const ENABLE_REALTIME = process.env.NEXT_PUBLIC_ENABLE_REALTIME === "true";

export type PresenceStatus = 'online' | 'offline' | 'away' | 'busy';

export interface UserPresence {
  userId: string;
  status: PresenceStatus;
  lastSeen: number;
}

export interface TypingIndicator {
  conversationId: string;
  typingUsers: string[];
}

interface UsePresenceOptions {
  autoAwayTimeout?: number; // milliseconds
  activityPingInterval?: number; // milliseconds
}

export function usePresence(options: UsePresenceOptions = {}) {
  const { data: session } = useSession();
  const [userPresence, setUserPresence] = useState<Map<string, UserPresence>>(new Map());
  const [typingIndicators, setTypingIndicators] = useState<TypingIndicator[]>([]);
  const [currentStatus, setCurrentStatus] = useState<PresenceStatus>('online');
  const [isConnected, setIsConnected] = useState(false);

  // PartyKit connection for presence
  const presenceConnection = usePartyKit("presence", "presence");
  
  // Refs for cleanup and state management
  const activityPingRef = useRef<NodeJS.Timeout>();
  const awayTimeoutRef = useRef<NodeJS.Timeout>();
  const typingTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const lastActivityRef = useRef<number>(Date.now());

  // Configuration
  const autoAwayTimeout = options.autoAwayTimeout || 5 * 60 * 1000; // 5 minutes
  const activityPingInterval = options.activityPingInterval || 30 * 1000; // 30 seconds

  // Update connection status
  useEffect(() => {
    setIsConnected(ENABLE_REALTIME && presenceConnection.connected);
  }, [presenceConnection.connected]);

  // Update presence status
  const updatePresenceStatus = useCallback((status: PresenceStatus) => {
    if (!ENABLE_REALTIME || !presenceConnection.connected || !session?.user?.id) return;

    setCurrentStatus(status);
    
    presenceConnection.sendMessage({
      type: 'presence_update',
      userId: session.user.id,
      status,
      timestamp: Date.now()
    });
  }, [presenceConnection, session?.user?.id]);

  // Track user activity
  const trackActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    
    // If we were away, mark as online
    if (currentStatus === 'away') {
      updatePresenceStatus('online');
    }

    // Reset away timeout
    if (awayTimeoutRef.current) {
      clearTimeout(awayTimeoutRef.current);
    }
    
    awayTimeoutRef.current = setTimeout(() => {
      if (currentStatus === 'online') {
        updatePresenceStatus('away');
      }
    }, autoAwayTimeout);
  }, [currentStatus, updatePresenceStatus, autoAwayTimeout]);

  // Send activity ping
  const sendActivityPing = useCallback(() => {
    if (!ENABLE_REALTIME || !presenceConnection.connected || !session?.user?.id) return;

    presenceConnection.sendMessage({
      type: 'activity_ping',
      userId: session.user.id,
      timestamp: Date.now()
    });
  }, [presenceConnection, session?.user?.id]);

  // Start typing indicator
  const startTyping = useCallback((conversationId: string) => {
    if (!ENABLE_REALTIME || !presenceConnection.connected || !session?.user?.id) return;

    presenceConnection.sendMessage({
      type: 'typing_start',
      userId: session.user.id,
      conversationId,
      timestamp: Date.now()
    });

    // Clear existing timeout for this conversation
    const existingTimeout = typingTimeoutsRef.current.get(conversationId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Auto-stop typing after 3 seconds
    const timeout = setTimeout(() => {
      stopTyping(conversationId);
    }, 3000);
    
    typingTimeoutsRef.current.set(conversationId, timeout);
  }, [presenceConnection, session?.user?.id]);

  // Stop typing indicator
  const stopTyping = useCallback((conversationId: string) => {
    if (!ENABLE_REALTIME || !presenceConnection.connected || !session?.user?.id) return;

    presenceConnection.sendMessage({
      type: 'typing_stop',
      userId: session.user.id,
      conversationId,
      timestamp: Date.now()
    });

    // Clear timeout
    const timeout = typingTimeoutsRef.current.get(conversationId);
    if (timeout) {
      clearTimeout(timeout);
      typingTimeoutsRef.current.delete(conversationId);
    }
  }, [presenceConnection, session?.user?.id]);

  // Get user presence
  const getUserPresence = useCallback((userId: string): UserPresence | null => {
    return userPresence.get(userId) || null;
  }, [userPresence]);

  // Check if user is online
  const isUserOnline = useCallback((userId: string): boolean => {
    const presence = userPresence.get(userId);
    return presence ? presence.status === 'online' : false;
  }, [userPresence]);

  // Get typing users for conversation
  const getTypingUsers = useCallback((conversationId: string): string[] => {
    const indicator = typingIndicators.find(t => t.conversationId === conversationId);
    return indicator?.typingUsers || [];
  }, [typingIndicators]);

  // Get online users
  const getOnlineUsers = useCallback((): string[] => {
    return Array.from(userPresence.values())
      .filter(presence => presence.status === 'online')
      .map(presence => presence.userId);
  }, [userPresence]);

  // Set up activity tracking
  useEffect(() => {
    if (!ENABLE_REALTIME || typeof window === 'undefined') return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      trackActivity();
    };

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Initial activity tracking
    trackActivity();

    return () => {
      // Remove event listeners
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      
      // Clear timeouts
      if (awayTimeoutRef.current) {
        clearTimeout(awayTimeoutRef.current);
      }
    };
  }, [trackActivity]);

  // Set up activity ping interval
  useEffect(() => {
    if (!ENABLE_REALTIME || !presenceConnection.connected) return;

    activityPingRef.current = setInterval(sendActivityPing, activityPingInterval);

    return () => {
      if (activityPingRef.current) {
        clearInterval(activityPingRef.current);
      }
    };
  }, [presenceConnection.connected, sendActivityPing, activityPingInterval]);

  // Handle presence updates from server
  useEffect(() => {
    if (!ENABLE_REALTIME || !presenceConnection.connected) return;

    const unsubscribePresenceUpdate = presenceConnection.onMessage('presence_update', (message: any) => {
      setUserPresence(prev => {
        const newMap = new Map(prev);
        newMap.set(message.userId, {
          userId: message.userId,
          status: message.status,
          lastSeen: message.timestamp
        });
        return newMap;
      });
    });

    const unsubscribeTypingUpdate = presenceConnection.onMessage('typing_update', (message: any) => {
      setTypingIndicators(prev => {
        const filtered = prev.filter(indicator => indicator.conversationId !== message.conversationId);
        
        if (message.typingUsers && message.typingUsers.length > 0) {
          return [...filtered, {
            conversationId: message.conversationId,
            typingUsers: message.typingUsers.filter((userId: string) => userId !== session?.user?.id) // Exclude self
          }];
        }
        
        return filtered;
      });
    });

    const unsubscribeConnectionConfirmed = presenceConnection.onMessage('presence_connection_confirmed', (message: any) => {
      // Initialize presence data from server
      if (message.currentPresence) {
        const presenceMap = new Map<string, UserPresence>();
        Object.entries(message.currentPresence).forEach(([userId, data]: [string, any]) => {
          presenceMap.set(userId, {
            userId,
            status: data.status,
            lastSeen: data.lastSeen
          });
        });
        setUserPresence(presenceMap);
      }
    });

    return () => {
      unsubscribePresenceUpdate();
      unsubscribeTypingUpdate();
      unsubscribeConnectionConfirmed();
    };
  }, [presenceConnection, session?.user?.id]);

  // Handle page visibility changes
  useEffect(() => {
    if (!ENABLE_REALTIME || typeof window === 'undefined') return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden, mark as away after a delay
        setTimeout(() => {
          if (document.hidden && currentStatus === 'online') {
            updatePresenceStatus('away');
          }
        }, 60000); // 1 minute delay
      } else {
        // Page is visible, mark as online
        if (currentStatus === 'away') {
          updatePresenceStatus('online');
        }
        trackActivity();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [currentStatus, updatePresenceStatus, trackActivity]);

  // Handle beforeunload to mark as offline
  useEffect(() => {
    if (!ENABLE_REALTIME || typeof window === 'undefined') return;

    const handleBeforeUnload = () => {
      updatePresenceStatus('offline');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [updatePresenceStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all typing timeouts
      for (const timeout of typingTimeoutsRef.current.values()) {
        clearTimeout(timeout);
      }
      typingTimeoutsRef.current.clear();
      
      // Mark as offline when component unmounts
      if (ENABLE_REALTIME && session?.user?.id) {
        updatePresenceStatus('offline');
      }
    };
  }, [updatePresenceStatus, session?.user?.id]);

  return {
    // State
    currentStatus,
    isConnected,
    userPresence: Array.from(userPresence.values()),
    typingIndicators,
    
    // Connection info
    connected: presenceConnection.connected,
    reconnecting: presenceConnection.reconnecting,
    latency: presenceConnection.latency,
    
    // Actions
    updatePresenceStatus,
    startTyping,
    stopTyping,
    trackActivity,
    
    // Utilities
    getUserPresence,
    isUserOnline,
    getTypingUsers,
    getOnlineUsers,
    
    // Stats
    onlineCount: getOnlineUsers().length,
    totalTrackedUsers: userPresence.size,
  };
}
