/**
 * PartyKit Real-time Functionality Tests
 * 
 * This test suite validates the real-time messaging, notifications,
 * and presence features implemented with PartyKit.
 */

import { renderHook, act } from '@testing-library/react';
import { useRealtimeMessaging } from '@/hooks/useRealtimeMessaging';
import { useRealtimeNotifications } from '@/hooks/useRealtimeNotifications';
import { usePresence } from '@/hooks/usePresence';
import { PartyKitManager } from '@/lib/partykit/client';
import { FallbackManager } from '@/lib/partykit/fallback';
import { PerformanceMonitor } from '@/lib/partykit/performance';

// Mock PartySocket
jest.mock('partysocket', () => {
  return jest.fn().mockImplementation(() => ({
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    send: jest.fn(),
    close: jest.fn(),
    readyState: WebSocket.OPEN,
  }));
});

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User'
      }
    }
  })
}));

// Mock environment variables
const originalEnv = process.env;
beforeEach(() => {
  process.env = {
    ...originalEnv,
    NEXT_PUBLIC_PARTYKIT_HOST: 'localhost:1999',
    NEXT_PUBLIC_ENABLE_REALTIME: 'true',
    NEXT_PUBLIC_ENABLE_REALTIME_MESSAGING: 'true',
    NEXT_PUBLIC_ENABLE_REALTIME_NOTIFICATIONS: 'true',
    NEXT_PUBLIC_PARTYKIT_MIGRATION_PHASE: 'development'
  };
});

afterEach(() => {
  process.env = originalEnv;
});

describe('PartyKit Client Manager', () => {
  let manager: PartyKitManager;

  beforeEach(() => {
    manager = new PartyKitManager();
    manager.initialize('test-user-123', 'test-session-123');
  });

  afterEach(() => {
    manager.disconnectAll();
  });

  test('should initialize with user credentials', () => {
    expect(manager).toBeDefined();
  });

  test('should connect to PartyKit room', () => {
    const socket = manager.connect('test-room');
    expect(socket).toBeDefined();
  });

  test('should send messages to connected room', () => {
    const socket = manager.connect('test-room');
    const success = manager.sendMessage('test-room', { type: 'test', content: 'hello' });
    expect(success).toBe(true);
  });

  test('should handle message listeners', () => {
    const handler = jest.fn();
    manager.onMessage('test_message', handler);
    
    // Simulate message
    const testMessage = { type: 'test_message', content: 'test' };
    // This would normally be called by the socket event handler
    
    manager.offMessage('test_message', handler);
  });

  test('should track connection status', () => {
    const statusHandler = jest.fn();
    manager.onConnectionStatus(statusHandler);
    
    // Connection status changes would be tested here
    manager.offConnectionStatus(statusHandler);
  });
});

describe('Real-time Messaging Hook', () => {
  test('should initialize with default state', () => {
    const { result } = renderHook(() => useRealtimeMessaging());
    
    expect(result.current.conversations).toEqual([]);
    expect(result.current.messages).toEqual([]);
    expect(result.current.selectedConversation).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isSending).toBe(false);
  });

  test('should send message successfully', async () => {
    const { result } = renderHook(() => useRealtimeMessaging());
    
    await act(async () => {
      const success = await result.current.sendMessage('receiver-123', 'Hello World');
      // In a real test, we'd mock the API response
      // expect(success).toBe(true);
    });
  });

  test('should handle typing indicators', () => {
    const { result } = renderHook(() => useRealtimeMessaging());
    
    act(() => {
      result.current.startTyping('conversation-123');
    });
    
    act(() => {
      result.current.stopTyping('conversation-123');
    });
    
    const typingUsers = result.current.getTypingUsers('conversation-123');
    expect(Array.isArray(typingUsers)).toBe(true);
  });

  test('should select conversation and fetch messages', () => {
    const { result } = renderHook(() => useRealtimeMessaging());
    
    act(() => {
      result.current.selectConversation('user-456');
    });
    
    expect(result.current.selectedConversation).toBe('user-456');
  });
});

describe('Real-time Notifications Hook', () => {
  test('should initialize with default state', () => {
    const { result } = renderHook(() => useRealtimeNotifications());
    
    expect(result.current.notifications).toEqual([]);
    expect(result.current.notificationCounts.total).toBe(0);
    expect(result.current.notificationCounts.unread).toBe(0);
    expect(result.current.isLoading).toBe(true);
  });

  test('should mark notification as read', async () => {
    const { result } = renderHook(() => useRealtimeNotifications());
    
    await act(async () => {
      await result.current.markAsRead('notification-123');
    });
    
    // Verify the notification was marked as read
  });

  test('should mark all notifications as read', async () => {
    const { result } = renderHook(() => useRealtimeNotifications());
    
    await act(async () => {
      await result.current.markAllAsRead();
    });
  });

  test('should delete notification', async () => {
    const { result } = renderHook(() => useRealtimeNotifications());
    
    await act(async () => {
      await result.current.deleteNotification('notification-123');
    });
  });

  test('should calculate notification counts correctly', () => {
    const { result } = renderHook(() => useRealtimeNotifications());
    
    const unreadCount = result.current.getUnreadCount();
    expect(typeof unreadCount).toBe('number');
    
    const highPriorityNotifications = result.current.getHighPriorityNotifications();
    expect(Array.isArray(highPriorityNotifications)).toBe(true);
  });
});

describe('Presence Hook', () => {
  test('should initialize with default state', () => {
    const { result } = renderHook(() => usePresence());
    
    expect(result.current.currentStatus).toBe('online');
    expect(result.current.isConnected).toBe(false);
    expect(Array.isArray(result.current.userPresence)).toBe(true);
    expect(Array.isArray(result.current.typingIndicators)).toBe(true);
  });

  test('should update presence status', () => {
    const { result } = renderHook(() => usePresence());
    
    act(() => {
      result.current.updatePresenceStatus('away');
    });
    
    expect(result.current.currentStatus).toBe('away');
  });

  test('should handle typing indicators', () => {
    const { result } = renderHook(() => usePresence());
    
    act(() => {
      result.current.startTyping('conversation-123');
    });
    
    act(() => {
      result.current.stopTyping('conversation-123');
    });
    
    const typingUsers = result.current.getTypingUsers('conversation-123');
    expect(Array.isArray(typingUsers)).toBe(true);
  });

  test('should check user online status', () => {
    const { result } = renderHook(() => usePresence());
    
    const isOnline = result.current.isUserOnline('user-123');
    expect(typeof isOnline).toBe('boolean');
  });

  test('should get online users list', () => {
    const { result } = renderHook(() => usePresence());
    
    const onlineUsers = result.current.getOnlineUsers();
    expect(Array.isArray(onlineUsers)).toBe(true);
  });
});

describe('Fallback Manager', () => {
  let fallbackManager: FallbackManager;

  beforeEach(() => {
    fallbackManager = new FallbackManager();
  });

  afterEach(() => {
    fallbackManager.destroy();
  });

  test('should initialize with default state', () => {
    const state = fallbackManager.getState();
    expect(state.mode).toBe('realtime');
    expect(state.canRetryRealtime).toBe(true);
    expect(state.connectionHealth.isHealthy).toBe(false);
  });

  test('should handle connection success', () => {
    fallbackManager.onConnectionSuccess();
    const state = fallbackManager.getState();
    expect(state.connectionHealth.isHealthy).toBe(true);
  });

  test('should handle connection failure', () => {
    fallbackManager.onConnectionFailure('Test error');
    const state = fallbackManager.getState();
    expect(state.connectionHealth.consecutiveFailures).toBeGreaterThan(0);
  });

  test('should update latency', () => {
    fallbackManager.onLatencyUpdate(150);
    const state = fallbackManager.getState();
    expect(state.connectionHealth.latency).toBe(150);
  });

  test('should force polling mode', () => {
    fallbackManager.forcePollingMode('Manual override');
    const state = fallbackManager.getState();
    expect(state.mode).toBe('polling');
    expect(state.reason).toBe('Manual override');
  });
});

describe('Performance Monitor', () => {
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    performanceMonitor = new PerformanceMonitor();
  });

  afterEach(() => {
    performanceMonitor.destroy();
  });

  test('should initialize with default metrics', () => {
    const metrics = performanceMonitor.getMetrics();
    expect(metrics.totalMessages).toBe(0);
    expect(metrics.failedMessages).toBe(0);
    expect(metrics.reconnectionCount).toBe(0);
  });

  test('should record latency', () => {
    performanceMonitor.recordLatency(100, 'message');
    const metrics = performanceMonitor.getMetrics();
    expect(metrics.messageLatency).toBe(100);
    expect(metrics.averageLatency).toBe(100);
  });

  test('should record message statistics', () => {
    performanceMonitor.recordMessage(true, 1024);
    const metrics = performanceMonitor.getMetrics();
    expect(metrics.totalMessages).toBe(1);
    expect(metrics.failedMessages).toBe(0);
    
    const stats = performanceMonitor.getConnectionStats();
    expect(stats.totalDataTransferred).toBe(1024);
  });

  test('should track connection lifecycle', () => {
    performanceMonitor.onConnectionStart();
    const metrics1 = performanceMonitor.getMetrics();
    expect(metrics1.lastConnectionTime).toBeDefined();
    
    performanceMonitor.onConnectionEnd();
    const metrics2 = performanceMonitor.getMetrics();
    expect(metrics2.connectionUptime).toBeGreaterThan(0);
  });

  test('should provide optimization recommendations', () => {
    // Simulate high latency
    performanceMonitor.recordLatency(600);
    
    const recommendations = performanceMonitor.getOptimizationRecommendations();
    expect(Array.isArray(recommendations)).toBe(true);
    expect(recommendations.length).toBeGreaterThan(0);
  });

  test('should generate performance report', () => {
    const report = performanceMonitor.generateReport();
    expect(typeof report).toBe('string');
    expect(report).toContain('PartyKit Performance Report');
  });
});

describe('Integration Tests', () => {
  test('should handle real-time message flow', async () => {
    // This would test the complete flow from sending a message
    // through PartyKit to receiving it on another client
    
    const { result: sender } = renderHook(() => useRealtimeMessaging());
    const { result: receiver } = renderHook(() => useRealtimeMessaging());
    
    // Simulate message sending and receiving
    await act(async () => {
      await sender.current.sendMessage('receiver-id', 'Test message');
    });
    
    // In a real integration test, we'd verify the message appears
    // in the receiver's messages array
  });

  test('should handle notification delivery', async () => {
    const { result } = renderHook(() => useRealtimeNotifications());
    
    // Simulate receiving a notification
    // In a real test, this would involve triggering a notification
    // from the server and verifying it appears in the client
  });

  test('should handle presence updates', () => {
    const { result: user1 } = renderHook(() => usePresence());
    const { result: user2 } = renderHook(() => usePresence());
    
    // Test presence synchronization between users
    act(() => {
      user1.current.updatePresenceStatus('away');
    });
    
    // In a real test, we'd verify user2 sees user1's status change
  });
});

describe('Error Handling', () => {
  test('should handle connection errors gracefully', () => {
    // Test error scenarios and fallback behavior
    const { result } = renderHook(() => useRealtimeMessaging());
    
    // Simulate connection error
    // Verify fallback to polling mode
  });

  test('should handle message sending failures', async () => {
    const { result } = renderHook(() => useRealtimeMessaging());
    
    // Test message sending failure scenarios
    await act(async () => {
      // Simulate network error
      const success = await result.current.sendMessage('invalid-user', 'Test');
      // Verify error handling
    });
  });

  test('should handle notification errors', async () => {
    const { result } = renderHook(() => useRealtimeNotifications());
    
    // Test notification error scenarios
    await act(async () => {
      await result.current.markAsRead('invalid-notification-id');
    });
  });
});
